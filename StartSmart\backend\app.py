from flask import Flask, render_template, send_from_directory
from flask_cors import CORS
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Import blueprints
from routes.ai_analysis import ai_analysis
from routes.auth import auth
from routes.applications import applications
from routes.admin import admin
from routes.iot_status import iot_status
from routes.agencies import agencies
from routes.documents import documents

app = Flask(__name__, static_folder='../static', template_folder='../frontend')
app.secret_key = os.getenv('SECRET_KEY', 'supersecretkey')

# Enable CORS for frontend integration
CORS(app)

# Register blueprints
app.register_blueprint(ai_analysis, url_prefix="/api/ai")
app.register_blueprint(auth, url_prefix="/api/auth")
app.register_blueprint(applications, url_prefix="/api/applications")
app.register_blueprint(admin, url_prefix="/api/admin")
app.register_blueprint(iot_status, url_prefix="/api/iot")
app.register_blueprint(agencies, url_prefix="/api/agencies")
app.register_blueprint(documents, url_prefix="/api/documents")

# Serve frontend
@app.route('/')
def index():
    return send_from_directory('../frontend', 'index.html')

@app.route('/<path:filename>')
def serve_static(filename):
    return send_from_directory('../frontend', filename)

if __name__ == "__main__":
    app.run(debug=True, host='0.0.0.0', port=5000)
