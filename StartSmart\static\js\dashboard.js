// Dashboard functionality

let currentUser = null;
let applications = [];
let agencies = [];

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    if (!requireAuth()) {
        return;
    }

    currentUser = getCurrentUser();

    // Update welcome message
    document.getElementById('userWelcome').textContent = `Welcome, ${currentUser.full_name}!`;

    // Show admin menu if user is admin
    if (currentUser.role === 'admin') {
        document.getElementById('adminMenu').style.display = 'block';
    }

    // Load initial data
    loadDashboardData();

    // Setup form handlers
    setupFormHandlers();
});

// Load dashboard data
async function loadDashboardData() {
    await Promise.all([
        loadApplications(),
        loadAgencies(),
        updateStats()
    ]);
}

// Load applications
async function loadApplications() {
    try {
        const response = await apiCall('/applications/');
        if (response && response.ok) {
            applications = await response.json();
            updateApplicationsDisplay();
            updateRecentApplications();
            populateApplicationDropdown();
        }
    } catch (error) {
        console.error('Error loading applications:', error);
    }
}

// Load agencies
async function loadAgencies() {
    try {
        const response = await apiCall('/agencies/list');
        if (response && response.ok) {
            agencies = await response.json();
            updateAgenciesDisplay();
        }
    } catch (error) {
        console.error('Error loading agencies:', error);
    }
}

// Update statistics
async function updateStats() {
    const totalApps = applications.length;
    const pendingApps = applications.filter(app => app.status === 'submitted').length;
    const approvedApps = applications.filter(app => app.status === 'approved').length;
    const rejectedApps = applications.filter(app => app.status === 'rejected').length;

    document.getElementById('totalApplications').textContent = totalApps;
    document.getElementById('pendingApplications').textContent = pendingApps;
    document.getElementById('approvedApplications').textContent = approvedApps;
    document.getElementById('rejectedApplications').textContent = rejectedApps;

    // Load admin stats if admin
    if (currentUser.role === 'admin') {
        await loadAdminStats();
    }
}

// Load admin statistics
async function loadAdminStats() {
    try {
        const response = await apiCall('/admin/statistics');
        if (response && response.ok) {
            const stats = await response.json();

            document.getElementById('adminTotalUsers').textContent = stats.total_users || 0;
            document.getElementById('adminTotalApplications').textContent = stats.total_applications || 0;
            document.getElementById('adminPendingApplications').textContent = stats.pending_applications || 0;

            const approvalRate = stats.total_applications > 0
                ? Math.round((stats.approved_applications / stats.total_applications) * 100)
                : 0;
            document.getElementById('adminApprovalRate').textContent = `${approvalRate}%`;
        }
    } catch (error) {
        console.error('Error loading admin stats:', error);
    }
}

// Show section
function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => section.style.display = 'none');

    // Show selected section
    document.getElementById(`${sectionName}-section`).style.display = 'block';

    // Update active nav link
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => link.classList.remove('active'));
    event.target.classList.add('active');

    // Load section-specific data
    switch(sectionName) {
        case 'my-applications':
            updateApplicationsDisplay();
            break;
        case 'documents':
            loadDocuments();
            break;
        case 'agencies':
            updateAgenciesDisplay();
            break;
        case 'admin-dashboard':
            if (currentUser.role === 'admin') {
                loadAdminApplications();
            }
            break;
    }
}

// Update applications display
function updateApplicationsDisplay() {
    const container = document.getElementById('applicationsList');

    if (applications.length === 0) {
        container.innerHTML = '<p class="text-muted">No applications found. <a href="#" onclick="showSection(\'new-application\')">Create your first application</a></p>';
        return;
    }

    let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr>';
    html += '<th>Application #</th><th>Business Name</th><th>Type</th><th>Status</th><th>Created</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    applications.forEach(app => {
        const statusClass = getStatusClass(app.status);
        html += `<tr>
            <td>${app.application_number}</td>
            <td>${app.business_name}</td>
            <td>${app.business_type}</td>
            <td><span class="badge ${statusClass}">${app.status}</span></td>
            <td>${formatDate(app.created_at)}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewApplication('${app._id}')">
                    <i class="fas fa-eye"></i> View
                </button>
            </td>
        </tr>`;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Update recent applications
function updateRecentApplications() {
    const container = document.getElementById('recentApplicationsList');
    const recentApps = applications.slice(0, 5);

    if (recentApps.length === 0) {
        container.innerHTML = '<p class="text-muted">No applications yet.</p>';
        return;
    }

    let html = '';
    recentApps.forEach(app => {
        const statusClass = getStatusClass(app.status);
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div>
                    <strong>${app.business_name}</strong><br>
                    <small class="text-muted">${app.business_type} • ${formatDate(app.created_at)}</small>
                </div>
                <span class="badge ${statusClass}">${app.status}</span>
            </div>
        `;
    });

    container.innerHTML = html;
}

// Get status class for badges
function getStatusClass(status) {
    switch(status) {
        case 'approved': return 'bg-success';
        case 'rejected': return 'bg-danger';
        case 'submitted': return 'bg-warning';
        case 'in_review': return 'bg-info';
        default: return 'bg-secondary';
    }
}

// Setup form handlers
function setupFormHandlers() {
    // New application form
    const newAppForm = document.getElementById('newApplicationForm');
    if (newAppForm) {
        newAppForm.addEventListener('submit', handleNewApplication);
    }

    // Document upload form
    const docForm = document.getElementById('documentUploadForm');
    if (docForm) {
        docForm.addEventListener('submit', handleDocumentUpload);
    }
}

// Handle new application submission
async function handleNewApplication(e) {
    e.preventDefault();

    const formData = {
        business_name: document.getElementById('businessName').value,
        business_type: document.getElementById('businessType').value,
        location: document.getElementById('location').value,
        applicant_name: document.getElementById('applicantName').value,
        contact_email: document.getElementById('contactEmail').value,
        description: document.getElementById('businessDescription').value
    };

    try {
        const response = await apiCall('/applications/', {
            method: 'POST',
            body: JSON.stringify(formData)
        });

        if (response && response.ok) {
            const result = await response.json();
            showAlert('success', 'Application submitted successfully!');

            // Reset form
            e.target.reset();

            // Reload applications
            await loadApplications();

            // Show applications section
            showSection('my-applications');
        } else {
            const error = await response.json();
            showAlert('error', error.error || 'Failed to submit application');
        }
    } catch (error) {
        showAlert('error', 'Network error. Please try again.');
    }
}

// Get AI Analysis
async function getAIAnalysis() {
    const formData = {
        business_name: document.getElementById('businessName').value,
        business_type: document.getElementById('businessType').value,
        location: document.getElementById('location').value,
        description: document.getElementById('businessDescription').value
    };

    // Validate required fields
    if (!formData.business_type || !formData.location || !formData.description) {
        showAlert('error', 'Please fill in business type, location, and description first.');
        return;
    }

    try {
        const response = await apiCall('/ai/', {
            method: 'POST',
            body: JSON.stringify(formData)
        });

        if (response && response.ok) {
            const result = await response.json();
            displayAIAnalysis(result.analysis);
        } else {
            const error = await response.json();
            showAlert('error', error.error || 'AI analysis failed');
        }
    } catch (error) {
        showAlert('error', 'Network error. Please try again.');
    }
}

// Display AI Analysis results
function displayAIAnalysis(analysis) {
    const container = document.getElementById('aiAnalysisContent');
    const resultsDiv = document.getElementById('aiAnalysisResults');

    let html = `
        <div class="row">
            <div class="col-md-6">
                <h6>Risk Assessment</h6>
                <p><strong>Level:</strong> ${analysis.risk_assessment.level}</p>
                <p><strong>Score:</strong> ${(analysis.risk_assessment.score * 100).toFixed(1)}%</p>

                <h6>Timeline Estimate</h6>
                <p>${analysis.timeline_estimate}</p>

                <h6>Success Probability</h6>
                <p>${(analysis.success_probability * 100).toFixed(1)}%</p>
            </div>
            <div class="col-md-6">
                <h6>Required Approvals</h6>
                <ul class="list-group list-group-flush">
    `;

    analysis.required_approvals.forEach(approval => {
        html += `<li class="list-group-item">${approval}</li>`;
    });

    html += `
                </ul>
            </div>
        </div>

        <div class="mt-3">
            <h6>Recommendations</h6>
            <ul>
    `;

    analysis.recommendations.forEach(rec => {
        html += `<li>${rec}</li>`;
    });

    html += `
            </ul>
        </div>

        <div class="mt-3">
            <h6>Estimated Costs</h6>
            <p><strong>Government Fees:</strong> ${formatCurrency(analysis.estimated_costs.government_fees)}</p>
            <p><strong>Professional Fees:</strong> ${formatCurrency(analysis.estimated_costs.professional_fees)}</p>
            <p><strong>Total Estimated:</strong> ${formatCurrency(analysis.estimated_costs.total_estimated)}</p>
        </div>
    `;

    container.innerHTML = html;
    resultsDiv.style.display = 'block';
}

// Update agencies display
function updateAgenciesDisplay() {
    const container = document.getElementById('agenciesList');

    if (!agencies || Object.keys(agencies).length === 0) {
        container.innerHTML = '<p class="text-muted">No agencies data available.</p>';
        return;
    }

    let html = '<div class="row">';

    Object.entries(agencies).forEach(([key, agency]) => {
        html += `
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6>${agency.name}</h6>
                    </div>
                    <div class="card-body">
                        <p class="card-text">${agency.description}</p>
                        <p><strong>Processing Time:</strong> ${agency.processing_time}</p>
                        <p><strong>Fees:</strong> ${formatCurrency(agency.fees)}</p>

                        <h6>Required Documents:</h6>
                        <ul class="list-unstyled">
        `;

        agency.required_documents.forEach(doc => {
            html += `<li><i class="fas fa-file-alt text-primary"></i> ${doc}</li>`;
        });

        html += `
                        </ul>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

// Populate application dropdown for documents
function populateApplicationDropdown() {
    const dropdown = document.getElementById('documentApplication');
    if (!dropdown) return;

    dropdown.innerHTML = '<option value="">Select Application</option>';

    applications.forEach(app => {
        const option = document.createElement('option');
        option.value = app._id;
        option.textContent = `${app.business_name} (${app.application_number})`;
        dropdown.appendChild(option);
    });
}

// Handle document upload
async function handleDocumentUpload(e) {
    e.preventDefault();

    const applicationId = document.getElementById('documentApplication').value;
    const documentType = document.getElementById('documentType').value;
    const fileInput = document.getElementById('documentFile');

    if (!applicationId || !documentType || !fileInput.files[0]) {
        showAlert('error', 'Please fill in all fields and select a file.');
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('application_id', applicationId);
    formData.append('document_type', documentType);

    try {
        const response = await fetch('/api/documents/upload', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            showAlert('success', 'Document uploaded successfully!');

            // Reset form
            e.target.reset();

            // Reload documents
            loadDocuments();
        } else {
            const error = await response.json();
            showAlert('error', error.error || 'Upload failed');
        }
    } catch (error) {
        showAlert('error', 'Network error. Please try again.');
    }
}

// Load documents
async function loadDocuments() {
    const container = document.getElementById('documentsList');

    if (applications.length === 0) {
        container.innerHTML = '<p class="text-muted">No applications found. Create an application first.</p>';
        return;
    }

    try {
        let allDocuments = [];

        // Load documents for each application
        for (const app of applications) {
            const response = await apiCall(`/documents/list/${app._id}`);
            if (response && response.ok) {
                const docs = await response.json();
                docs.forEach(doc => {
                    doc.application_name = app.business_name;
                });
                allDocuments = allDocuments.concat(docs);
            }
        }

        if (allDocuments.length === 0) {
            container.innerHTML = '<p class="text-muted">No documents uploaded yet.</p>';
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr>';
        html += '<th>Application</th><th>Document Type</th><th>Filename</th><th>Uploaded</th><th>Status</th>';
        html += '</tr></thead><tbody>';

        allDocuments.forEach(doc => {
            const statusClass = doc.verified ? 'bg-success' : 'bg-warning';
            const statusText = doc.verified ? 'Verified' : 'Pending';

            html += `<tr>
                <td>${doc.application_name}</td>
                <td>${doc.document_type}</td>
                <td>${doc.filename}</td>
                <td>${formatDate(doc.uploaded_at)}</td>
                <td><span class="badge ${statusClass}">${statusText}</span></td>
            </tr>`;
        });

        html += '</tbody></table></div>';
        container.innerHTML = html;

    } catch (error) {
        console.error('Error loading documents:', error);
        container.innerHTML = '<p class="text-danger">Error loading documents.</p>';
    }
}

// View application details
function viewApplication(applicationId) {
    const app = applications.find(a => a._id === applicationId);
    if (!app) return;

    // Create modal content
    const modalHtml = `
        <div class="modal fade" id="applicationModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Application Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Application #:</strong> ${app.application_number}</p>
                                <p><strong>Business Name:</strong> ${app.business_name}</p>
                                <p><strong>Business Type:</strong> ${app.business_type}</p>
                                <p><strong>Location:</strong> ${app.location}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Applicant:</strong> ${app.applicant_name}</p>
                                <p><strong>Email:</strong> ${app.contact_email}</p>
                                <p><strong>Status:</strong> <span class="badge ${getStatusClass(app.status)}">${app.status}</span></p>
                                <p><strong>Created:</strong> ${formatDate(app.created_at)}</p>
                            </div>
                        </div>
                        <div class="mt-3">
                            <p><strong>Description:</strong></p>
                            <p>${app.description}</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById('applicationModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
    modal.show();
}

// Load admin applications
async function loadAdminApplications() {
    try {
        const response = await apiCall('/admin/all-applications');
        if (response && response.ok) {
            const data = await response.json();
            const adminApps = data.applications || data;
            displayAdminApplications(adminApps);
        }
    } catch (error) {
        console.error('Error loading admin applications:', error);
    }
}

// Display admin applications
function displayAdminApplications(adminApps) {
    const container = document.getElementById('adminApplicationsList');

    if (adminApps.length === 0) {
        container.innerHTML = '<p class="text-muted">No applications found.</p>';
        return;
    }

    let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr>';
    html += '<th>Application #</th><th>Business Name</th><th>Applicant</th><th>Type</th><th>Status</th><th>Created</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    adminApps.forEach(app => {
        const statusClass = getStatusClass(app.status);
        html += `<tr>
            <td>${app.application_number}</td>
            <td>${app.business_name}</td>
            <td>${app.applicant_name}</td>
            <td>${app.business_type}</td>
            <td><span class="badge ${statusClass}">${app.status}</span></td>
            <td>${formatDate(app.created_at)}</td>
            <td>
                <button class="btn btn-sm btn-success me-1" onclick="approveApplication('${app._id}')"
                        ${app.status === 'approved' ? 'disabled' : ''}>
                    <i class="fas fa-check"></i> Approve
                </button>
                <button class="btn btn-sm btn-danger" onclick="rejectApplication('${app._id}')"
                        ${app.status === 'rejected' ? 'disabled' : ''}>
                    <i class="fas fa-times"></i> Reject
                </button>
            </td>
        </tr>`;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Approve application (admin)
async function approveApplication(applicationId) {
    const comments = prompt('Enter approval comments (optional):');

    try {
        const response = await apiCall(`/admin/application/${applicationId}/approve`, {
            method: 'POST',
            body: JSON.stringify({ comments: comments || '' })
        });

        if (response && response.ok) {
            showAlert('success', 'Application approved successfully!');
            loadAdminApplications();
        } else {
            const error = await response.json();
            showAlert('error', error.error || 'Failed to approve application');
        }
    } catch (error) {
        showAlert('error', 'Network error. Please try again.');
    }
}

// Reject application (admin)
async function rejectApplication(applicationId) {
    const comments = prompt('Enter rejection reason (required):');

    if (!comments) {
        showAlert('error', 'Rejection reason is required.');
        return;
    }

    try {
        const response = await apiCall(`/admin/application/${applicationId}/reject`, {
            method: 'POST',
            body: JSON.stringify({ comments })
        });

        if (response && response.ok) {
            showAlert('success', 'Application rejected.');
            loadAdminApplications();
        } else {
            const error = await response.json();
            showAlert('error', error.error || 'Failed to reject application');
        }
    } catch (error) {
        showAlert('error', 'Network error. Please try again.');
    }
}