import tkinter as tk

def update_status(status):
    if status == "Approved":
        canvas.itemconfig(circle, fill="green")
    elif status == "Pending":
        canvas.itemconfig(circle, fill="yellow")
    else:
        canvas.itemconfig(circle, fill="red")

root = tk.Tk()
root.title("Approval Status Indicator")
canvas = tk.<PERSON>(root, width=200, height=200)
canvas.pack()
circle = canvas.create_oval(50, 50, 150, 150, fill="gray")

update_status("Approved")  # Change status to test
root.mainloop()
