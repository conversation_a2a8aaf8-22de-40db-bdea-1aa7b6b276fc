from flask import Blueprint, request, jsonify, session

auth = Blueprint("auth", __name__)

users = {"admin": "admin123", "user": "pass123"}

@auth.route("/login", methods=["POST"])
def login():
    data = request.json
    if users.get(data["username"]) == data["password"]:
        session["user"] = data["username"]
        return jsonify({"message": "Login successful"})
    return jsonify({"message": "Invalid credentials"}), 401

@auth.route("/logout", methods=["POST"])
def logout():
    session.pop("user", None)
    return jsonify({"message": "Logged out"})
