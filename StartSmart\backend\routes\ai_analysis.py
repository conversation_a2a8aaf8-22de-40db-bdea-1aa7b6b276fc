from flask import Blueprint, request, jsonify
import openai
import os

ai_analysis = Blueprint("ai_analysis", __name__)

# Check if API key exists
if not os.getenv(""):
    raise ValueError("OPENAI_API_KEY environment variable not set")
openai.api_key = os.getenv("OPENAI_API_KEY")

@ai_analysis.route("/", methods=["POST"])
def analyze():
    # Validate request data
    if not request.is_json:
        return jsonify({"error": "Request must be JSON"}), 400
    
    data = request.json
    required_fields = ['business_type', 'location', 'description']
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"Missing required field: {field}"}), 400

    # Create prompt with proper string formatting
    prompt = (
        f"Analyze this {data['business_type']} business located at {data['location']}: "
        f"{data['description']}"
    )

    try:
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}]
        )
        ai_result = response["choices"][0]["message"]["content"]
        return jsonify({"analysis": ai_result})
    except openai.error.OpenAIError as e:
        return jsonify({"error": str(e)}), 500
    except Exception as e:
        return jsonify({"error": "An unexpected error occurred"}), 500