<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - StartSmart</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .progress-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-3 text-white">
                        <h4><i class="fas fa-rocket"></i> StartSmart</h4>
                        <small id="userWelcome">Welcome!</small>
                    </div>
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#" onclick="showSection('overview')">
                            <i class="fas fa-tachometer-alt"></i> Overview
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('new-application')">
                            <i class="fas fa-plus-circle"></i> New Application
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('my-applications')">
                            <i class="fas fa-file-alt"></i> My Applications
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('documents')">
                            <i class="fas fa-folder"></i> Documents
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('agencies')">
                            <i class="fas fa-building"></i> Agencies
                        </a>
                        <div id="adminMenu" style="display: none;">
                            <a class="nav-link" href="#" onclick="showSection('admin-dashboard')">
                                <i class="fas fa-cog"></i> Admin Dashboard
                            </a>
                        </div>
                        <a class="nav-link" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content p-4">
                    <!-- Overview Section -->
                    <div id="overview-section" class="content-section">
                        <h2>Dashboard Overview</h2>

                        <!-- Stats Cards -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                                        <h5 class="card-title" id="totalApplications">0</h5>
                                        <p class="card-text">Total Applications</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                        <h5 class="card-title" id="pendingApplications">0</h5>
                                        <p class="card-text">Pending</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                        <h5 class="card-title" id="approvedApplications">0</h5>
                                        <p class="card-text">Approved</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                                        <h5 class="card-title" id="rejectedApplications">0</h5>
                                        <p class="card-text">Rejected</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Applications -->
                        <div class="card">
                            <div class="card-header">
                                <h5>Recent Applications</h5>
                            </div>
                            <div class="card-body">
                                <div id="recentApplicationsList">
                                    <p class="text-muted">Loading...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- New Application Section -->
                    <div id="new-application-section" class="content-section" style="display: none;">
                        <h2>Create New Application</h2>

                        <div class="card">
                            <div class="card-body">
                                <form id="newApplicationForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="businessName" class="form-label">Business Name *</label>
                                                <input type="text" class="form-control" id="businessName" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="businessType" class="form-label">Business Type *</label>
                                                <select class="form-select" id="businessType" required>
                                                    <option value="">Select Business Type</option>
                                                    <option value="retail">Retail</option>
                                                    <option value="manufacturing">Manufacturing</option>
                                                    <option value="restaurant">Restaurant</option>
                                                    <option value="tech">Technology</option>
                                                    <option value="healthcare">Healthcare</option>
                                                    <option value="education">Education</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="location" class="form-label">Location *</label>
                                                <select class="form-select" id="location" required>
                                                    <option value="">Select Location</option>
                                                    <option value="mumbai">Mumbai</option>
                                                    <option value="delhi">Delhi</option>
                                                    <option value="bangalore">Bangalore</option>
                                                    <option value="chennai">Chennai</option>
                                                    <option value="hyderabad">Hyderabad</option>
                                                    <option value="pune">Pune</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="applicantName" class="form-label">Applicant Name *</label>
                                                <input type="text" class="form-control" id="applicantName" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="contactEmail" class="form-label">Contact Email *</label>
                                        <input type="email" class="form-control" id="contactEmail" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="businessDescription" class="form-label">Business Description *</label>
                                        <textarea class="form-control" id="businessDescription" rows="4" required
                                                  placeholder="Describe your business, products/services, target market, etc."></textarea>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="button" class="btn btn-secondary me-md-2" onclick="getAIAnalysis()">
                                            <i class="fas fa-robot"></i> Get AI Analysis
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i> Submit Application
                                        </button>
                                    </div>
                                </form>

                                <!-- AI Analysis Results -->
                                <div id="aiAnalysisResults" style="display: none;" class="mt-4">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <h6><i class="fas fa-robot"></i> AI Analysis Results</h6>
                                        </div>
                                        <div class="card-body" id="aiAnalysisContent">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- My Applications Section -->
                    <div id="my-applications-section" class="content-section" style="display: none;">
                        <h2>My Applications</h2>

                        <div class="card">
                            <div class="card-body">
                                <div id="applicationsList">
                                    <p class="text-muted">Loading...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Documents Section -->
                    <div id="documents-section" class="content-section" style="display: none;">
                        <h2>Document Management</h2>

                        <div class="card">
                            <div class="card-header">
                                <h5>Upload Documents</h5>
                            </div>
                            <div class="card-body">
                                <form id="documentUploadForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="documentApplication" class="form-label">Application</label>
                                                <select class="form-select" id="documentApplication" required>
                                                    <option value="">Select Application</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="documentType" class="form-label">Document Type</label>
                                                <select class="form-select" id="documentType" required>
                                                    <option value="">Select Document Type</option>
                                                    <option value="Business Plan">Business Plan</option>
                                                    <option value="Identity Proof">Identity Proof</option>
                                                    <option value="Address Proof">Address Proof</option>
                                                    <option value="Bank Statement">Bank Statement</option>
                                                    <option value="Other">Other</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="documentFile" class="form-label">Choose File</label>
                                        <input type="file" class="form-control" id="documentFile" required>
                                    </div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload"></i> Upload Document
                                    </button>
                                </form>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5>My Documents</h5>
                            </div>
                            <div class="card-body">
                                <div id="documentsList">
                                    <p class="text-muted">No documents uploaded yet.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Agencies Section -->
                    <div id="agencies-section" class="content-section" style="display: none;">
                        <h2>Government Agencies</h2>

                        <div class="card">
                            <div class="card-body">
                                <div id="agenciesList">
                                    <p class="text-muted">Loading agencies...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Admin Dashboard Section -->
                    <div id="admin-dashboard-section" class="content-section" style="display: none;">
                        <h2>Admin Dashboard</h2>

                        <!-- Admin Stats -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                        <h5 class="card-title" id="adminTotalUsers">0</h5>
                                        <p class="card-text">Total Users</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-file-alt fa-2x text-info mb-2"></i>
                                        <h5 class="card-title" id="adminTotalApplications">0</h5>
                                        <p class="card-text">Total Applications</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                        <h5 class="card-title" id="adminPendingApplications">0</h5>
                                        <p class="card-text">Pending Review</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                                        <h5 class="card-title" id="adminApprovalRate">0%</h5>
                                        <p class="card-text">Approval Rate</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- All Applications Table -->
                        <div class="card">
                            <div class="card-header">
                                <h5>All Applications</h5>
                            </div>
                            <div class="card-body">
                                <div id="adminApplicationsList">
                                    <p class="text-muted">Loading...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../static/js/auth.js"></script>
    <script src="../static/js/dashboard.js"></script>
</body>
</html>